import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { User, Lock, Heart, Shield } from "lucide-react";
import healthcareHero from "@/assets/healthcare-learning-hero.jpg";

interface LoginPageProps {
  onLogin: (role: string) => void;
}

const LoginPage = ({ onLogin }: LoginPageProps) => {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [credentials, setCredentials] = useState({
    username: "",
    password: ""
  });

  const roles = [
    { value: "learner", label: "Learner", description: "Healthcare professional seeking training" },
    { value: "trainer", label: "Trainer", description: "Training program instructor" },
    { value: "manager", label: "Manager", description: "Department head or supervisor" },
    { value: "leadership", label: "Leadership", description: "Senior leadership team" },
    { value: "administrator", label: "Administrator", description: "System administrator" }
  ];

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedRole && credentials.username && credentials.password) {
      onLogin(selectedRole);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/30 flex items-center justify-center p-4">
      {/* Background Hero Image */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url(${healthcareHero})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      />
      
      <div className="w-full max-w-md relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-primary p-3 rounded-full shadow-lg">
              <Heart className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Medcare LMS
          </h1>
          <p className="text-muted-foreground">
            Professional Healthcare Learning Platform
          </p>
        </div>

        {/* Login Card */}
        <Card className="bg-card/95 backdrop-blur-sm shadow-card border-border/50">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center gap-2">
              <Shield className="w-5 h-5 text-primary" />
              Secure Login
            </CardTitle>
            <CardDescription className="text-center">
              Select your role and enter your credentials
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-6">
              {/* Role Selection */}
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger id="role" className="bg-background">
                    <SelectValue placeholder="Select your role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.value} value={role.value}>
                        <div>
                          <div className="font-medium">{role.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {role.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Username */}
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter your username"
                    value={credentials.username}
                    onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                    className="pl-10 bg-background"
                    required
                  />
                </div>
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    className="pl-10 bg-background"
                    required
                  />
                </div>
              </div>

              {/* Login Button */}
              <Button 
                type="submit" 
                variant="medical" 
                className="w-full"
                disabled={!selectedRole || !credentials.username || !credentials.password}
              >
                Sign In
              </Button>

              {/* Additional Links */}
              <div className="text-center space-y-2 text-sm">
                <button type="button" className="text-primary hover:underline">
                  Forgot password?
                </button>
                <div className="text-muted-foreground">
                  Need help? Contact IT Support
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-muted-foreground">
          <p>© 2025 Namaa by Aster With knowledge and action, we elevate care</p>
          <p>Secure • Professional • Trusted</p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;