import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3 } from "lucide-react";

const SimpleChart = () => {
  const options = {
    chart: {
      type: 'column',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Training Hours by Month'
    },
    xAxis: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
    },
    yAxis: {
      title: {
        text: 'Hours'
      }
    },
    series: [{
      name: 'Training Hours',
      data: [5, 8, 12, 15, 18, 25],
      color: '#3b82f6'
    }],
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-primary" />
          Simple Chart Test
        </CardTitle>
        <CardDescription>
          Basic Highcharts implementation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default SimpleChart;
