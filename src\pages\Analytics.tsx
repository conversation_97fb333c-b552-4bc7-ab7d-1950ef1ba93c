import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  Clock, 
  Award, 
  BookOpen,
  Target,
  Users
} from "lucide-react";

const Analytics = () => {
  const analyticsData = {
    trainingHours: 40,
    trainingDays: 5,
    activeCertifications: 3,
    auditsCompleted: 2,
    seaScore: 50,
    assessmentsCompleted: "2/3",
    completedPrograms: 5,
    nominatedTrainings: 8,
    trainingActualization: 63
  };

  const monthlyProgress = [
    { month: "Jan", completed: 2, target: 3 },
    { month: "Feb", completed: 1, target: 2 },
    { month: "Mar", completed: 3, target: 3 },
    { month: "Apr", completed: 2, target: 4 },
    { month: "May", completed: 1, target: 2 },
    { month: "Jun", completed: 3, target: 3 },
  ];

  const performanceMetrics = [
    { 
      label: "Training Hours", 
      value: analyticsData.trainingHours, 
      target: 50, 
      icon: Clock,
      color: "primary" 
    },
    { 
      label: "Training Days", 
      value: analyticsData.trainingDays, 
      target: 8, 
      icon: Calendar,
      color: "success" 
    },
    { 
      label: "Active Certifications", 
      value: analyticsData.activeCertifications, 
      target: 5, 
      icon: Award,
      color: "accent" 
    },
    { 
      label: "Audits Completed", 
      value: analyticsData.auditsCompleted, 
      target: 4, 
      icon: Target,
      color: "warning" 
    }
  ];

  const getProgressPercentage = (value: number, target: number) => {
    return Math.min((value / target) * 100, 100);
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "success";
    if (percentage >= 60) return "primary";
    if (percentage >= 40) return "warning";
    return "destructive";
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Learning Analytics</h1>
          <p className="text-muted-foreground">Comprehensive overview of your learning progress and performance metrics</p>
        </div>
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
          Current Year
        </Badge>
      </div>

      {/* Key Metrics - Matching the mockup */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Training Hours</p>
                <p className="text-2xl font-bold text-primary">{analyticsData.trainingHours}</p>
              </div>
              <Clock className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Training Days</p>
                <p className="text-2xl font-bold text-success">{analyticsData.trainingDays}</p>
              </div>
              <Calendar className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Certifications</p>
                <p className="text-2xl font-bold text-accent">{analyticsData.activeCertifications}</p>
              </div>
              <Award className="w-8 h-8 text-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Audits Conducted</p>
                <p className="text-2xl font-bold text-warning">{analyticsData.auditsCompleted}</p>
              </div>
              <Target className="w-8 h-8 text-warning" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SEA Score</p>
                <p className="text-2xl font-bold text-primary">{analyticsData.seaScore}%</p>
              </div>
              <BarChart3 className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Assessments</p>
                <p className="text-2xl font-bold text-accent">{analyticsData.assessmentsCompleted}</p>
              </div>
              <BookOpen className="w-8 h-8 text-accent" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-primary" />
              Performance vs Targets
            </CardTitle>
            <CardDescription>
              Track your progress against annual learning targets
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {performanceMetrics.map((metric, index) => {
              const percentage = getProgressPercentage(metric.value, metric.target);
              return (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <metric.icon className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{metric.label}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {metric.value}/{metric.target}
                    </span>
                  </div>
                  <Progress 
                    value={percentage} 
                    className="h-2"
                  />
                  <div className="text-xs text-muted-foreground">
                    {percentage.toFixed(0)}% of target achieved
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              Monthly Training Completion
            </CardTitle>
            <CardDescription>
              Training programs completed each month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyProgress.map((month, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{month.month}</span>
                    <span className="text-sm text-muted-foreground">
                      {month.completed}/{month.target}
                    </span>
                  </div>
                  <Progress 
                    value={getProgressPercentage(month.completed, month.target)} 
                    className="h-2"
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Training Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="text-lg">Training Actualization</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {analyticsData.trainingActualization}%
              </div>
              <p className="text-sm text-muted-foreground">
                Overall completion rate
              </p>
              <Progress value={analyticsData.trainingActualization} className="mt-4" />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="text-lg">Program Distribution</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">Completed</span>
              <Badge variant="default">{analyticsData.completedPrograms}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Nominated</span>
              <Badge variant="default">{analyticsData.nominatedTrainings}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">In Progress</span>
              <Badge variant="secondary">3</Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="text-lg">Learning Insights</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-3 bg-success/10 rounded-lg">
              <p className="text-sm font-medium text-success">Strong Performer</p>
              <p className="text-xs text-muted-foreground">Above average completion rate</p>
            </div>
            <div className="p-3 bg-primary/10 rounded-lg">
              <p className="text-sm font-medium text-primary">Active Learner</p>
              <p className="text-xs text-muted-foreground">Consistent training participation</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SEA Score Note */}
      <Card className="bg-accent/5 border-accent/20">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-sm">
            <BarChart3 className="w-4 h-4 text-accent" />
            <span className="font-medium">Note:</span>
            <span>SEA - Service Excellence Audit Score. Analytics can be represented in the form of graphs/numbers</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;