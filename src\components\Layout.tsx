import { useState } from "react";
import { NavLink } from "react-router-dom";
import {
  BookOpen,
  Calendar,
  Award,
  GraduationCap,
  BarChart3,
  Home,
  Clock,
  User,
  LogOut,
  Bell,
  Settings
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface LayoutProps {
  children: React.ReactNode;
  onLogout: () => void;
}

const navigationItems = [
  { title: "Dashboard", url: "/dashboard", icon: Home },
  { title: "Nominated Trainings", url: "/nominated-trainings", icon: BookOpen },
  { title: "Upcoming Trainings", url: "/upcoming-trainings", icon: Clock },
  { title: "Active Certifications", url: "/active-certifications", icon: Award },
  { title: "Learning Calendar", url: "/learning-calendar", icon: Calendar },
  { title: "Learning Summary", url: "/learning-summary", icon: GraduationCap },
  { title: "Analytics", url: "/analytics", icon: BarChart3 },
];

export function Layout({ children, onLogout }: LayoutProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleSidebar = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const getNavClassName = ({ isActive }: { isActive: boolean }) => {
    return cn(
      "flex items-center text-sm font-medium rounded-lg transition-all duration-200 min-w-0",
      "hover:bg-primary/10 hover:text-primary hover:shadow-sm",
      isMenuOpen
        ? "gap-3 px-4 py-3 w-full"
        : "justify-center p-3 mx-2 w-auto",
      isActive
        ? "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-md border border-primary/20"
        : "text-muted-foreground hover:text-foreground"
    );
  };

  return (
    <div className="h-screen flex flex-col w-full overflow-hidden">
      {/* Fixed Header */}
      <header className="h-16 bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50 border-b border-border shadow-sm flex-shrink-0 z-50">
        <div className="flex h-full items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <GraduationCap className="w-4 h-4 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-foreground">Namaa by Aster</h1>
                <p className="text-xs text-muted-foreground">With knowledge and action, we elevate care</p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Bell className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Content Area with Fixed Sidebar */}
      <div className="flex flex-1 overflow-hidden">
        {/* Fixed Sidebar */}
        <div
          className={cn(
            "bg-card border-r border-border shadow-lg transition-all duration-300 ease-in-out flex-shrink-0 z-40 overflow-hidden h-full cursor-pointer",
            isMenuOpen ? "w-80 hover:bg-muted/10" : "w-16 hover:bg-muted/30"
          )}
          onClick={(e) => {
            e.stopPropagation();
            toggleSidebar();
          }}
          title={isMenuOpen ? "Click to collapse sidebar" : "Click to expand sidebar"}
        >
          <div className="flex flex-col h-full min-w-0">



            {/* Navigation Menu - Scrollable Content */}
            <div className={cn(
              "flex-1 overflow-y-auto overflow-x-hidden transition-all duration-300 min-h-0",
              isMenuOpen ? "p-4" : "py-4 px-1"
            )}>
              <div className="space-y-6">
                {/* Main Navigation */}
                <div>
                  {isMenuOpen && (
                    <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider mb-3 px-3">
                      Navigation
                    </h3>
                  )}
                  <div className={cn(
                    "transition-all duration-300",
                    isMenuOpen ? "space-y-1" : "space-y-2"
                  )}>
                    {navigationItems.map((item) => (
                      <NavLink
                        key={item.title}
                        to={item.url}
                        className={getNavClassName}
                        title={!isMenuOpen ? item.title : undefined}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <item.icon className="w-5 h-5 flex-shrink-0" />
                        {isMenuOpen && <span>{item.title}</span>}
                      </NavLink>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Fixed Sidebar Footer */}
            <div className={cn(
              "border-t border-border bg-muted/30 transition-all duration-300 overflow-hidden flex-shrink-0",
              isMenuOpen ? "p-4" : "py-4 px-1"
            )}>
              <div className={cn(
                "transition-all duration-300 min-w-0",
                isMenuOpen ? "space-y-2" : "space-y-3"
              )}>
                <NavLink
                  to="/profile"
                  className={getNavClassName}
                  title={!isMenuOpen ? "Profile Settings" : undefined}
                  onClick={(e) => e.stopPropagation()}
                >
                  <User className="w-5 h-5 flex-shrink-0" />
                  {isMenuOpen && <span>Profile Settings</span>}
                </NavLink>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onLogout();
                  }}
                  className={cn(
                    "text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-200 min-w-0",
                    isMenuOpen
                      ? "justify-start px-4 py-3 w-full"
                      : "justify-center p-3 mx-2 w-auto"
                  )}
                  title={!isMenuOpen ? "Logout" : undefined}
                >
                  <LogOut className="w-5 h-5 flex-shrink-0" />
                  {isMenuOpen && <span className="ml-3">Logout</span>}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Scrollable */}
        <main className="flex-1 overflow-auto min-w-0 h-full">
          {children}
        </main>
      </div>


    </div>
  );
}