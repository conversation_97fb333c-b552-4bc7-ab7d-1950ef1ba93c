import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp, PieChart, Activity } from "lucide-react";

const LearningAnalyticsCharts = () => {
  // Simple test to ensure Highcharts is working
  if (!Highcharts) {
    return <div>Loading charts...</div>;
  }
  // Training Progress Over Time (Line Chart)
  const trainingProgressOptions = {
    chart: {
      type: 'line',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Training Progress Over Time',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      title: {
        text: 'Months'
      }
    },
    yAxis: {
      title: {
        text: 'Hours Completed'
      },
      min: 0
    },
    series: [{
      name: 'Training Hours',
      data: [5, 8, 12, 15, 18, 25],
      color: '#3b82f6'
    }, {
      name: 'Target Hours',
      data: [10, 15, 20, 25, 30, 35],
      color: '#ef4444',
      dashStyle: 'dash'
    }],
    legend: {
      enabled: true
    },
    credits: {
      enabled: false
    }
  };

  // Training Types Distribution (Pie Chart)
  const trainingTypesOptions = {
    chart: {
      type: 'pie',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Training Types Distribution',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    series: [{
      name: 'Training Types',
      data: [
        { name: 'Online Training', y: 45, color: '#3b82f6' },
        { name: 'Classroom Training', y: 35, color: '#10b981' },
        { name: 'Practical Training', y: 15, color: '#f59e0b' },
        { name: 'Assessment', y: 5, color: '#ef4444' }
      ]
    }],
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f} %'
        },
        showInLegend: true
      }
    },
    credits: {
      enabled: false
    }
  };

  // Certification Status (Column Chart)
  const certificationStatusOptions = {
    chart: {
      type: 'column',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Certification Status by Department',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Emergency', 'ICU', 'Surgery', 'Pediatrics', 'Cardiology'],
      title: {
        text: 'Departments'
      }
    },
    yAxis: {
      title: {
        text: 'Number of Certifications'
      },
      min: 0
    },
    series: [{
      name: 'Active',
      data: [8, 6, 9, 5, 7],
      color: '#10b981'
    }, {
      name: 'Expired',
      data: [2, 3, 1, 4, 2],
      color: '#ef4444'
    }, {
      name: 'Pending',
      data: [3, 2, 4, 2, 3],
      color: '#f59e0b'
    }],
    plotOptions: {
      column: {
        stacking: 'normal'
      }
    },
    credits: {
      enabled: false
    }
  };

  // SEA Score Trend (Area Chart)
  const seaScoreOptions = {
    chart: {
      type: 'area',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'SEA Score Trend',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    xAxis: {
      categories: ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024'],
      title: {
        text: 'Quarters'
      }
    },
    yAxis: {
      title: {
        text: 'SEA Score (%)'
      },
      min: 0,
      max: 100
    },
    series: [{
      name: 'SEA Score',
      data: [45, 52, 48, 55],
      color: '#8b5cf6',
      fillOpacity: 0.3
    }],
    plotOptions: {
      area: {
        marker: {
          enabled: true,
          symbol: 'circle',
          radius: 4
        }
      }
    },
    credits: {
      enabled: false
    }
  };

  // Performance Donut Chart
  const performanceDonutOptions = {
    chart: {
      type: 'pie',
      height: 300,
      backgroundColor: 'transparent',
    },
    title: {
      text: 'Overall Performance Score',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
      }
    },
    series: [{
      name: 'Performance',
      data: [
        { name: 'Completed', y: 75, color: '#10b981' },
        { name: 'Remaining', y: 25, color: '#e5e7eb' }
      ],
      innerSize: '60%',
      dataLabels: {
        enabled: false
      }
    }],
    plotOptions: {
      pie: {
        allowPointSelect: false,
        cursor: 'pointer',
        dataLabels: {
          enabled: false
        },
        showInLegend: true,
        center: ['50%', '50%']
      }
    },
    legend: {
      enabled: true,
      layout: 'horizontal',
      align: 'center',
      verticalAlign: 'bottom'
    },
    credits: {
      enabled: false
    }
  };

  return (
    <div className="space-y-6">
      {/* First Row - Main Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Training Progress Chart */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-primary" />
              Training Progress
            </CardTitle>
            <CardDescription>
              Monthly training hours vs targets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HighchartsReact
              highcharts={Highcharts}
              options={trainingProgressOptions}
            />
          </CardContent>
        </Card>

        {/* Performance Donut */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-primary" />
              Overall Performance
            </CardTitle>
            <CardDescription>
              Combined performance score
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HighchartsReact
              highcharts={Highcharts}
              options={performanceDonutOptions}
            />
          </CardContent>
        </Card>
      </div>

      {/* Second Row - Distribution and Status Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Training Types Distribution */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5 text-primary" />
              Training Types
            </CardTitle>
            <CardDescription>
              Distribution of training methods
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HighchartsReact
              highcharts={Highcharts}
              options={trainingTypesOptions}
            />
          </CardContent>
        </Card>

        {/* Certification Status */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              Certification Status
            </CardTitle>
            <CardDescription>
              Certification status across departments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HighchartsReact
              highcharts={Highcharts}
              options={certificationStatusOptions}
            />
          </CardContent>
        </Card>
      </div>

      {/* Third Row - Trend Analysis */}
      <div className="grid grid-cols-1 gap-6">
        {/* SEA Score Trend */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-primary" />
              SEA Score Trend
            </CardTitle>
            <CardDescription>
              Service Excellence Audit score over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HighchartsReact
              highcharts={Highcharts}
              options={seaScoreOptions}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LearningAnalyticsCharts;
